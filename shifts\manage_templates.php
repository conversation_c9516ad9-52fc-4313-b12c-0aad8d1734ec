<?php
session_start();

// Include permissions system
require_once __DIR__ . '/../includes/permissions.php';
require_once __DIR__ . '/../includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require shift template management permissions
requireAnyPermission($mysqli, ['shifts.view', 'shifts.create', 'shifts.edit']);

$msg = "";
$error = "";

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'add_template') {
      if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.create')) {
        $error = "You don't have permission to create shift templates.";
      } else {
        $name = trim($_POST['name'] ?? '');
        $start_time = $_POST['start_time'] ?? '';
        $end_time = $_POST['end_time'] ?? '';
        $desc = trim($_POST['description'] ?? '');

        // Attendance policy fields
        $early_checkin_allowed = floatval($_POST['early_checkin_allowed_hours'] ?? 1.0);
        $late_checkin_grace = floatval($_POST['late_checkin_grace_hours'] ?? 0.5);
        $late_checkin_policy = $_POST['late_checkin_policy'] ?? 'mark_late';
        $early_checkout_penalty = floatval($_POST['early_checkout_penalty_hours'] ?? 0.5);
        $late_checkout_overtime = floatval($_POST['late_checkout_overtime_hours'] ?? 2.0);
        $overtime_tracking = isset($_POST['overtime_tracking_enabled']) ? 1 : 0;
        $weekend_policy_enabled = isset($_POST['weekend_policy_enabled']) ? 1 : 0;
        $holiday_policy_enabled = isset($_POST['holiday_policy_enabled']) ? 1 : 0;

        if ($name && $start_time && $end_time) {
          $stmt = $mysqli->prepare("
            INSERT INTO shift_templates (
              name, start_time, end_time, description,
              early_checkin_allowed_hours, late_checkin_grace_hours, late_checkin_policy,
              early_checkout_penalty_hours, late_checkout_overtime_hours, overtime_tracking_enabled,
              weekend_policy_enabled, holiday_policy_enabled
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ");
          $stmt->bind_param('ssssddsddiiii',
            $name, $start_time, $end_time, $desc,
            $early_checkin_allowed, $late_checkin_grace, $late_checkin_policy,
            $early_checkout_penalty, $late_checkout_overtime, $overtime_tracking,
            $weekend_policy_enabled, $holiday_policy_enabled
          );
          if ($stmt->execute()) {
            $msg = "Shift template \"$name\" created successfully with attendance policies.";
          } else {
            $error = "Failed to create shift template. It may already exist.";
          }
          $stmt->close();
        } else {
          $error = "Please fill in all required fields.";
        }
      }
    } elseif ($action === 'edit_template') {
      if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit')) {
        $error = "You don't have permission to edit shift templates.";
      } else {
        $template_id = intval($_POST['template_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $start_time = $_POST['start_time'] ?? '';
        $end_time = $_POST['end_time'] ?? '';
        $desc = trim($_POST['description'] ?? '');

        // Attendance policy fields
        $early_checkin_allowed = floatval($_POST['early_checkin_allowed_hours'] ?? 1.0);
        $late_checkin_grace = floatval($_POST['late_checkin_grace_hours'] ?? 0.5);
        $late_checkin_policy = $_POST['late_checkin_policy'] ?? 'mark_late';
        $early_checkout_penalty = floatval($_POST['early_checkout_penalty_hours'] ?? 0.5);
        $late_checkout_overtime = floatval($_POST['late_checkout_overtime_hours'] ?? 2.0);
        $overtime_tracking = isset($_POST['overtime_tracking_enabled']) ? 1 : 0;
        $weekend_policy_enabled = isset($_POST['weekend_policy_enabled']) ? 1 : 0;
        $holiday_policy_enabled = isset($_POST['holiday_policy_enabled']) ? 1 : 0;

        if ($template_id && $name && $start_time && $end_time) {
          $stmt = $mysqli->prepare("
            UPDATE shift_templates SET
              name = ?, start_time = ?, end_time = ?, description = ?,
              early_checkin_allowed_hours = ?, late_checkin_grace_hours = ?, late_checkin_policy = ?,
              early_checkout_penalty_hours = ?, late_checkout_overtime_hours = ?, overtime_tracking_enabled = ?,
              weekend_policy_enabled = ?, holiday_policy_enabled = ?
            WHERE id = ?
          ");
          $stmt->bind_param('ssssddsddiiii',
            $name, $start_time, $end_time, $desc,
            $early_checkin_allowed, $late_checkin_grace, $late_checkin_policy,
            $early_checkout_penalty, $late_checkout_overtime, $overtime_tracking,
            $weekend_policy_enabled, $holiday_policy_enabled, $template_id
          );
          if ($stmt->execute()) {
            $msg = "Shift template \"$name\" updated successfully.";
          } else {
            $error = "Failed to update shift template.";
          }
          $stmt->close();
        } else {
          $error = "Please fill in all required fields.";
        }
      }
    } elseif ($action === 'delete_template') {
      if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.delete')) {
        $error = "You don't have permission to delete shift templates.";
      } else {
        $template_id = intval($_POST['template_id'] ?? 0);
        if ($template_id) {
          // Check if template is being used
          $check_stmt = $mysqli->prepare("SELECT COUNT(*) as count FROM employee_shifts WHERE shift_template_id = ?");
          $check_stmt->bind_param('i', $template_id);
          $check_stmt->execute();
          $check_stmt->bind_result($usage_count);
          $check_stmt->fetch();
          $check_stmt->close();

          if ($usage_count > 0) {
            $error = "Cannot delete this template as it is currently assigned to $usage_count shift(s).";
          } else {
            $stmt = $mysqli->prepare("DELETE FROM shift_templates WHERE id = ?");
            $stmt->bind_param('i', $template_id);
            if ($stmt->execute()) {
              $msg = "Shift template deleted successfully.";
            } else {
              $error = "Failed to delete shift template.";
            }
            $stmt->close();
          }
        }
      }
    }
  }
}

// Fetch shift templates with attendance policies
$templates_result = $mysqli->query("
  SELECT *,
    CASE
      WHEN weekend_policy_enabled = 1 THEN 'Weekend policies enabled'
      ELSE ''
    END as weekend_status,
    CASE
      WHEN holiday_policy_enabled = 1 THEN 'Holiday policies enabled'
      ELSE ''
    END as holiday_status
  FROM shift_templates
  ORDER BY name
");
$shift_templates = $templates_result->fetch_all(MYSQLI_ASSOC);

// Check user permissions for UI
$can_create = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.create');
$can_edit = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit');
$can_delete = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.delete');
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Create Shift Templates - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">
            <i class="bi bi-plus-circle me-2"></i>
            Create Shift Templates
          </h1>
          <p class="page-subtitle">Create new shift templates with attendance policies</p>
        </div>
        <div>
          <a href="templates.php" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Templates
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success alert-dismissible fade show">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger alert-dismissible fade show">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <?php endif; ?>

    <!-- Create New Template -->
    <?php if ($can_create): ?>
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-plus-circle me-2"></i>
        Create New Shift Template
      </h2>
      <form method="POST" class="row g-3">
        <input type="hidden" name="action" value="add_template" />

        <div class="col-md-6">
          <label for="name" class="form-label">Template Name</label>
          <input type="text" class="form-control" id="name" name="name" required
                 placeholder="e.g., Morning Shift, Night Shift" />
        </div>

        <div class="col-md-3">
          <label for="start_time" class="form-label">Start Time</label>
          <input type="time" class="form-control" id="start_time" name="start_time" required />
        </div>

        <div class="col-md-3">
          <label for="end_time" class="form-label">End Time</label>
          <input type="time" class="form-control" id="end_time" name="end_time" required />
        </div>

        <div class="col-12">
          <label for="description" class="form-label">Description (Optional)</label>
          <textarea class="form-control" id="description" name="description" rows="2"
                    placeholder="Brief description of this shift template"></textarea>
        </div>

        <!-- Attendance Policies Section -->
        <div class="col-12">
          <hr class="my-4">
          <h5 class="mb-3">
            <i class="bi bi-clock-history me-2"></i>
            Attendance Policies
          </h5>
        </div>

        <div class="col-md-6">
          <label for="early_checkin_allowed_hours" class="form-label">Early Check-in Allowed (hours)</label>
          <input type="number" step="0.25" min="0" max="24" class="form-control"
                 id="early_checkin_allowed_hours" name="early_checkin_allowed_hours" value="1.0" />
          <div class="form-text">Hours before shift start allowed for overtime calculation</div>
        </div>

        <div class="col-md-6">
          <label for="late_checkin_grace_hours" class="form-label">Late Check-in Grace Period (hours)</label>
          <input type="number" step="0.25" min="0" max="24" class="form-control"
                 id="late_checkin_grace_hours" name="late_checkin_grace_hours" value="0.5" />
          <div class="form-text">Grace period for late arrivals without penalty</div>
        </div>

        <div class="col-md-6">
          <label for="late_checkin_policy" class="form-label">Late Check-in Policy</label>
          <select class="form-select" id="late_checkin_policy" name="late_checkin_policy">
            <option value="mark_late">Mark as Late</option>
            <option value="mark_absent">Mark as Absent</option>
          </select>
          <div class="form-text">Action for arrivals beyond grace period</div>
        </div>

        <div class="col-md-6">
          <label for="early_checkout_penalty_hours" class="form-label">Early Check-out Penalty (hours)</label>
          <input type="number" step="0.25" min="0" max="24" class="form-control"
                 id="early_checkout_penalty_hours" name="early_checkout_penalty_hours" value="0.5" />
          <div class="form-text">Penalty hours for early departures</div>
        </div>

        <div class="col-md-6">
          <label for="late_checkout_overtime_hours" class="form-label">Late Check-out Overtime Limit (hours)</label>
          <input type="number" step="0.25" min="0" max="24" class="form-control"
                 id="late_checkout_overtime_hours" name="late_checkout_overtime_hours" value="2.0" />
          <div class="form-text">Hours after shift end allowed for overtime</div>
        </div>

        <div class="col-12">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="overtime_tracking_enabled" name="overtime_tracking_enabled" checked>
            <label class="form-check-label" for="overtime_tracking_enabled">
              Track Overtime Hours
            </label>
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="weekend_policy_enabled" name="weekend_policy_enabled">
            <label class="form-check-label" for="weekend_policy_enabled">
              Enable Different Weekend Policies
            </label>
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="holiday_policy_enabled" name="holiday_policy_enabled">
            <label class="form-check-label" for="holiday_policy_enabled">
              Enable Different Holiday Policies
            </label>
          </div>
        </div>

        <div class="col-12">
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            Create Template
          </button>
        </div>
      </form>
    </div>
    <?php endif; ?>


  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>